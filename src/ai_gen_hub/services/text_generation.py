"""
AI Gen Hub 文本生成服务

提供统一的文本生成接口，整合所有供应商的文本生成能力，包括：
- 统一的请求/响应格式
- 参数标准化和映射
- 流式输出支持
- 结果后处理和优化
"""

import asyncio
import time
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from uuid import uuid4

from ai_gen_hub.cache import CacheInterface
from ai_gen_hub.config.settings import Settings
from ai_gen_hub.core.exceptions import (
    InvalidRequestError,
    ModelNotSupportedError,
    ProviderUnavailableError,
)
from ai_gen_hub.core.interfaces import (
    ModelType,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    OptimizedTextGenerationRequest,
    RequestAdapter,
)
from ai_gen_hub.core.logging import LoggerMixin, add_request_context
from ai_gen_hub.monitoring import metrics_collector
from ai_gen_hub.services.provider_manager import AIProviderManager
from ai_gen_hub.services.router import RequestRouter
from ai_gen_hub.utils import (
    Re<PERSON><PERSON>anager,
    DEFAULT_RETRY_CONFIG,
    circuit_breaker_manager,
)


class TextGenerationService(LoggerMixin):
    """文本生成服务"""
    
    def __init__(
        self,
        settings: Settings,
        provider_manager: AIProviderManager,
        router: RequestRouter,
        cache: Optional[CacheInterface] = None
    ):
        """初始化文本生成服务
        
        Args:
            settings: 应用配置
            provider_manager: 供应商管理器
            router: 请求路由器
            cache: 缓存接口
        """
        self.settings = settings
        self.provider_manager = provider_manager
        self.router = router
        self.cache = cache
        
        # 重试管理器
        self.retry_manager = RetryManager(DEFAULT_RETRY_CONFIG)
        
        # 支持的功能
        self.features = {
            "streaming": settings.features.enable_streaming,
            "caching": settings.features.enable_caching and cache is not None,
            "load_balancing": settings.features.enable_load_balancing,
            "circuit_breaker": settings.features.enable_circuit_breaker,
        }
        
        self.logger.info(
            "文本生成服务初始化完成",
            features=self.features
        )
    
    async def generate_text(
        self,
        request: TextGenerationRequest,
        user_id: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """生成文本（传统接口，保持向后兼容）

        Args:
            request: 文本生成请求
            user_id: 用户ID
            request_id: 请求ID

        Returns:
            文本生成响应或流式响应迭代器

        Raises:
            InvalidRequestError: 请求参数无效
            ModelNotSupportedError: 模型不支持
            ProviderUnavailableError: 供应商不可用
        """
        # 转换为优化版本进行处理
        optimized_request = RequestAdapter.adapt_request(request)
        return await self.generate_text_optimized(optimized_request, user_id, request_id)

    async def generate_text_optimized(
        self,
        request: Union[TextGenerationRequest, OptimizedTextGenerationRequest, Dict[str, Any]],
        user_id: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """生成文本（优化版本接口）

        这个方法支持多种请求格式，并提供增强的供应商兼容性检查。

        Args:
            request: 文本生成请求（支持传统格式、优化版本或字典格式）
            user_id: 用户ID
            request_id: 请求ID

        Returns:
            文本生成响应或流式响应迭代器

        Raises:
            InvalidRequestError: 请求参数无效
            ModelNotSupportedError: 模型不支持
            ProviderUnavailableError: 供应商不可用
        """
        # 适配请求格式
        optimized_request = RequestAdapter.adapt_request(request)

        # 设置请求上下文
        if request_id is None:
            request_id = str(uuid4())
        
        add_request_context(request_id=request_id, user_id=user_id)

        # 验证请求
        self._validate_optimized_request(optimized_request)

        # 记录请求开始
        start_time = time.time()
        self.logger.info(
            "开始文本生成（优化版本）",
            model=optimized_request.model,
            stream=optimized_request.stream.enabled,
            max_tokens=optimized_request.generation.max_tokens,
            temperature=optimized_request.generation.temperature,
            request_type=type(request).__name__
        )
        
        try:
            # 检查缓存（仅对非流式请求）
            if not optimized_request.stream.enabled and self.features["caching"]:
                # 使用传统格式进行缓存查询（保持兼容性）
                legacy_request = optimized_request.to_legacy_format()
                cached_response = await self._get_cached_response_legacy(legacy_request, user_id)
                if cached_response:
                    self.logger.info("返回缓存结果")
                    metrics_collector.record_cache_operation("text_generation", "l1", "hit")
                    return cached_response
                else:
                    metrics_collector.record_cache_operation("text_generation", "l1", "miss")

            # 路由到合适的供应商
            provider = await self.router.route_request(
                ModelType.TEXT_GENERATION,
                optimized_request.model,
                optimized_request.to_legacy_format(),  # 路由器仍使用传统格式
                user_id
            )
            
            # 记录路由开始
            await self.router.record_request_start(provider.name)

            # 执行供应商兼容性检查
            validation_result = optimized_request.validate_for_provider(provider.name)
            if validation_result["errors"]:
                error_msg = f"请求与供应商 {provider.name} 不兼容: {validation_result['errors']}"
                self.logger.error(error_msg)
                raise InvalidRequestError(error_msg)

            # 记录兼容性警告
            for warning in validation_result["warnings"]:
                self.logger.warning(f"供应商兼容性警告: {warning}")

            # 记录优化建议
            for info in validation_result["info"]:
                self.logger.info(f"供应商优化建议: {info}")

            # 执行文本生成
            if self.features["circuit_breaker"]:
                # 使用熔断器
                circuit_breaker = circuit_breaker_manager.get_circuit_breaker(
                    f"text_generation_{provider.name}"
                )

                response = await circuit_breaker.call(
                    self._generate_text_with_provider_optimized,
                    provider,
                    optimized_request
                )
            else:
                # 直接调用
                response = await self._generate_text_with_provider_optimized(provider, optimized_request)
            
            # 处理响应
            if optimized_request.stream.enabled:
                # 流式响应
                return self._process_stream_response_optimized(
                    response, provider.name, start_time, optimized_request, user_id
                )
            else:
                # 普通响应
                processed_response = await self._process_response_optimized(
                    response, provider.name, start_time, optimized_request, user_id
                )

                # 缓存响应（使用传统格式保持兼容性）
                if self.features["caching"]:
                    legacy_request = optimized_request.to_legacy_format()
                    await self._cache_response_legacy(legacy_request, processed_response, user_id)

                return processed_response
                
        except Exception as e:
            # 记录错误
            duration = time.time() - start_time
            
            self.logger.error(
                "文本生成失败",
                error=str(e),
                error_type=type(e).__name__,
                duration=duration
            )
            
            # 记录指标
            metrics_collector.record_request(
                provider_name="unknown",
                model=request.model,
                request_type="text_generation",
                duration=duration,
                status="error"
            )
            
            raise
    
    def _validate_request(self, request: TextGenerationRequest) -> None:
        """验证请求参数
        
        Args:
            request: 文本生成请求
            
        Raises:
            InvalidRequestError: 请求参数无效
        """
        # 检查消息列表
        if not request.messages:
            raise InvalidRequestError("消息列表不能为空")
        
        # 检查模型名称
        if not request.model:
            raise InvalidRequestError("模型名称不能为空")
        
        # 检查参数范围
        if request.temperature is not None:
            if not 0.0 <= request.temperature <= 2.0:
                raise InvalidRequestError("temperature必须在0.0-2.0之间")
        
        if request.top_p is not None:
            if not 0.0 <= request.top_p <= 1.0:
                raise InvalidRequestError("top_p必须在0.0-1.0之间")
        
        if request.max_tokens is not None:
            if request.max_tokens <= 0:
                raise InvalidRequestError("max_tokens必须大于0")
        
        # 检查功能支持
        if request.stream and not self.features["streaming"]:
            raise InvalidRequestError("流式输出功能未启用")
    
    async def _get_cached_response(
        self,
        request: TextGenerationRequest,
        user_id: Optional[str]
    ) -> Optional[TextGenerationResponse]:
        """获取缓存的响应
        
        Args:
            request: 文本生成请求
            user_id: 用户ID
            
        Returns:
            缓存的响应或None
        """
        if not self.cache:
            return None
        
        try:
            # 生成缓存键
            cache_key = self.cache.key_generator.generate_key(
                "text_generation",
                request.model,
                request.dict(),
                user_id
            )
            
            # 获取缓存
            cached_data = await self.cache.get(cache_key)
            
            if cached_data:
                # 反序列化为响应对象
                return TextGenerationResponse(**cached_data)
            
            return None
            
        except Exception as e:
            self.logger.warning("获取缓存失败", error=str(e))
            return None
    
    async def _cache_response(
        self,
        request: TextGenerationRequest,
        response: TextGenerationResponse,
        user_id: Optional[str]
    ) -> None:
        """缓存响应
        
        Args:
            request: 文本生成请求
            response: 文本生成响应
            user_id: 用户ID
        """
        if not self.cache:
            return
        
        try:
            # 生成缓存键
            cache_key = self.cache.key_generator.generate_key(
                "text_generation",
                request.model,
                request.dict(),
                user_id
            )
            
            # 缓存响应
            await self.cache.set(
                cache_key,
                response.dict(),
                ttl=self.settings.cache.redis_cache_ttl
            )
            
        except Exception as e:
            self.logger.warning("缓存响应失败", error=str(e))
    
    async def _generate_text_with_provider(
        self,
        provider,
        request: TextGenerationRequest
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """使用指定供应商生成文本
        
        Args:
            provider: AI供应商
            request: 文本生成请求
            
        Returns:
            文本生成响应
        """
        # 使用重试机制
        return await self.retry_manager.execute_with_retry(
            provider.generate_text,
            request
        )
    
    async def _process_response(
        self,
        response: TextGenerationResponse,
        provider_name: str,
        start_time: float,
        request: TextGenerationRequest,
        user_id: Optional[str]
    ) -> TextGenerationResponse:
        """处理响应
        
        Args:
            response: 原始响应
            provider_name: 供应商名称
            start_time: 开始时间
            request: 原始请求
            user_id: 用户ID
            
        Returns:
            处理后的响应
        """
        duration = time.time() - start_time
        
        # 更新响应信息
        response.processing_time = duration
        response.request_id = str(uuid4())
        
        # 记录路由结束
        await self.router.record_request_end(
            provider_name, True, duration
        )
        
        # 记录指标
        metrics_collector.record_request(
            provider_name=provider_name,
            model=request.model,
            request_type="text_generation",
            duration=duration,
            status="success"
        )
        
        # 记录Token使用
        if response.usage:
            metrics_collector.record_token_usage(
                provider_name,
                request.model,
                "input",
                response.usage.prompt_tokens
            )
            metrics_collector.record_token_usage(
                provider_name,
                request.model,
                "output",
                response.usage.completion_tokens
            )
        
        self.logger.info(
            "文本生成完成",
            provider=provider_name,
            model=request.model,
            duration=duration,
            input_tokens=response.usage.prompt_tokens if response.usage else 0,
            output_tokens=response.usage.completion_tokens if response.usage else 0
        )
        
        return response
    
    async def _process_stream_response(
        self,
        response_stream: AsyncIterator[TextGenerationStreamChunk],
        provider_name: str,
        start_time: float,
        request: TextGenerationRequest,
        user_id: Optional[str]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理流式响应
        
        Args:
            response_stream: 响应流
            provider_name: 供应商名称
            start_time: 开始时间
            request: 原始请求
            user_id: 用户ID
            
        Yields:
            处理后的流式响应块
        """
        chunk_count = 0
        total_content_length = 0
        
        try:
            async for chunk in response_stream:
                chunk_count += 1
                
                # 更新块信息
                chunk.request_id = str(uuid4())
                
                # 统计内容长度
                for choice in chunk.choices:
                    if isinstance(choice, dict) and "delta" in choice:
                        delta = choice["delta"]
                        if isinstance(delta, dict) and "content" in delta:
                            content = delta["content"]
                            if isinstance(content, str):
                                total_content_length += len(content)
                
                yield chunk
            
            # 记录流式响应完成
            duration = time.time() - start_time
            
            await self.router.record_request_end(
                provider_name, True, duration
            )
            
            metrics_collector.record_request(
                provider_name=provider_name,
                model=request.model,
                request_type="text_generation_stream",
                duration=duration,
                status="success"
            )
            
            self.logger.info(
                "流式文本生成完成",
                provider=provider_name,
                model=request.model,
                duration=duration,
                chunk_count=chunk_count,
                content_length=total_content_length
            )
            
        except Exception as e:
            # 记录流式响应错误
            duration = time.time() - start_time
            
            await self.router.record_request_end(
                provider_name, False, duration, e
            )
            
            metrics_collector.record_request(
                provider_name=provider_name,
                model=request.model,
                request_type="text_generation_stream",
                duration=duration,
                status="error"
            )
            
            self.logger.error(
                "流式文本生成失败",
                provider=provider_name,
                model=request.model,
                duration=duration,
                chunk_count=chunk_count,
                error=str(e)
            )
            
            raise
    
    async def get_supported_models(self) -> Dict[str, List[str]]:
        """获取支持的模型列表
        
        Returns:
            按供应商分组的模型列表
        """
        supported_models = {}
        
        for provider_name in self.provider_manager.get_provider_list():
            provider = await self.provider_manager.get_provider(provider_name)
            if provider and provider.supports_model_type(ModelType.TEXT_GENERATION):
                provider_info = await provider.get_provider_info()
                supported_models[provider_name] = provider_info.models
        
        return supported_models
    
    async def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息
        
        Returns:
            服务统计信息
        """
        return {
            "service": "text_generation",
            "features": self.features,
            "supported_models": await self.get_supported_models(),
            "provider_stats": self.router.get_provider_statistics(),
        }

    # ========================================================================
    # 优化版本的辅助方法
    # ========================================================================

    def _validate_optimized_request(self, request: OptimizedTextGenerationRequest):
        """验证优化版本请求

        Args:
            request: 优化版本请求对象

        Raises:
            InvalidRequestError: 请求参数无效
        """
        try:
            # 基础验证已经由Pydantic完成

            # 检查消息列表
            if not request.messages:
                raise InvalidRequestError("消息列表不能为空")

            # 检查模型名称
            if not request.model.strip():
                raise InvalidRequestError("模型名称不能为空")

            # 检查生成配置的合理性
            if request.generation.temperature < 0 or request.generation.temperature > 2:
                raise InvalidRequestError("temperature必须在0-2之间")

            if request.generation.max_tokens is not None and request.generation.max_tokens <= 0:
                raise InvalidRequestError("max_tokens必须大于0")

            self.logger.debug("优化版本请求验证通过")

        except Exception as e:
            self.logger.error(f"优化版本请求验证失败: {e}")
            raise InvalidRequestError(f"请求验证失败: {e}")

    async def _generate_text_with_provider_optimized(
        self,
        provider: "AIProvider",
        request: OptimizedTextGenerationRequest
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """使用指定供应商生成文本（优化版本）

        Args:
            provider: AI供应商实例
            request: 优化版本请求对象

        Returns:
            文本生成响应或流式响应迭代器
        """
        try:
            # 检查供应商是否支持优化版本
            if hasattr(provider, 'generate_text_optimized'):
                # 供应商支持优化版本，直接调用
                self.logger.debug(f"使用 {provider.name} 的优化版本接口")
                return await provider.generate_text_optimized(request)
            else:
                # 供应商不支持优化版本，转换为传统格式
                self.logger.debug(f"将优化版本转换为传统格式调用 {provider.name}")
                legacy_request_dict = request.to_legacy_format()

                # 创建传统请求对象
                from .interfaces import TextGenerationRequest
                legacy_request = TextGenerationRequest(**legacy_request_dict)

                return await provider.generate_text(legacy_request)

        except Exception as e:
            self.logger.error(f"供应商 {provider.name} 文本生成失败: {e}")
            raise

    async def _process_response_optimized(
        self,
        response: TextGenerationResponse,
        provider_name: str,
        start_time: float,
        request: OptimizedTextGenerationRequest,
        user_id: Optional[str]
    ) -> TextGenerationResponse:
        """处理优化版本的普通响应

        Args:
            response: 原始响应
            provider_name: 供应商名称
            start_time: 请求开始时间
            request: 优化版本请求
            user_id: 用户ID

        Returns:
            处理后的响应
        """
        processing_time = time.time() - start_time

        # 记录请求完成
        await self.router.record_request_end(provider_name, True, processing_time)

        self.logger.info(
            "文本生成完成（优化版本）",
            provider=provider_name,
            processing_time=processing_time,
            stream=False,
            model=request.model,
            tokens_used=response.usage.total_tokens if response.usage else 0
        )

        return response

    def _process_stream_response_optimized(
        self,
        response: AsyncIterator[TextGenerationStreamChunk],
        provider_name: str,
        start_time: float,
        request: OptimizedTextGenerationRequest,
        user_id: Optional[str]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理优化版本的流式响应

        Args:
            response: 原始流式响应
            provider_name: 供应商名称
            start_time: 请求开始时间
            request: 优化版本请求
            user_id: 用户ID

        Returns:
            处理后的流式响应
        """
        self.logger.info(
            "开始流式文本生成（优化版本）",
            provider=provider_name,
            model=request.model,
            stream_config=request.stream.dict()
        )

        return response

    async def _get_cached_response_legacy(
        self,
        request: Dict[str, Any],
        user_id: Optional[str]
    ) -> Optional[TextGenerationResponse]:
        """获取缓存响应（传统格式兼容）"""
        if not self.cache:
            return None

        try:
            # 使用传统的缓存逻辑
            cache_key = self._generate_cache_key(request, user_id)
            cached_data = await self.cache.get(cache_key)

            if cached_data:
                return TextGenerationResponse(**cached_data)

            return None

        except Exception as e:
            self.logger.warning(f"缓存查询失败: {e}")
            return None

    async def _cache_response_legacy(
        self,
        request: Dict[str, Any],
        response: TextGenerationResponse,
        user_id: Optional[str]
    ):
        """缓存响应（传统格式兼容）"""
        if not self.cache:
            return

        try:
            cache_key = self._generate_cache_key(request, user_id)
            cache_data = response.dict()

            # 设置缓存过期时间（1小时）
            await self.cache.set(cache_key, cache_data, ttl=3600)

        except Exception as e:
            self.logger.warning(f"缓存存储失败: {e}")
