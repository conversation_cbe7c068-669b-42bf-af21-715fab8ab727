"""
AI Gen Hub 文本生成API路由

提供文本生成相关的API端点，包括：
- 文本生成（传统接口，保持向后兼容）
- 文本生成（优化版本接口，支持增强功能）
- 流式文本生成
- 支持的模型查询

版本更新：
- 添加 v2 API 端点支持优化版本请求
- 保持 v1 API 端点的向后兼容性
- 集成供应商兼容性检查和参数验证
"""

from typing import Any, Dict, List, Union

from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

from ai_gen_hub.core.interfaces import (
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    OptimizedTextGenerationRequest,
    RequestAdapter,
)
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.services import TextGenerationService


router = APIRouter()


def get_text_service(request: Request) -> TextGenerationService:
    """获取文本生成服务依赖"""
    service = getattr(request.app.state, "text_service", None)
    if not service:
        raise HTTPException(status_code=500, detail="文本生成服务未初始化")
    return service


def get_user_id(request: Request) -> str:
    """获取用户ID依赖"""
    user = getattr(request.state, "user", {})
    return user.get("user_id", "anonymous")


@router.post("/generate", response_model=Union[TextGenerationResponse, None])
async def generate_text(
    request_data: TextGenerationRequest,
    request: Request,
    text_service: TextGenerationService = Depends(get_text_service),
    user_id: str = Depends(get_user_id)
):
    """生成文本
    
    支持同步和流式两种模式：
    - 同步模式：返回完整的文本生成结果
    - 流式模式：返回Server-Sent Events流
    """
    try:
        if request_data.stream:
            # 流式响应
            async def generate_stream():
                async for chunk in await text_service.generate_text(
                    request_data,
                    user_id=user_id,
                    request_id=getattr(request.state, "request_id", None)
                ):
                    yield {
                        "event": "chunk",
                        "data": chunk.json()
                    }
                
                # 发送结束事件
                yield {
                    "event": "done",
                    "data": "[DONE]"
                }
            
            return EventSourceResponse(generate_stream())
        else:
            # 同步响应
            response = await text_service.generate_text(
                request_data,
                user_id=user_id,
                request_id=getattr(request.state, "request_id", None)
            )
            return response
            
    except AIGenHubException as e:
        raise HTTPException(status_code=400, detail=e.to_dict())
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/v2/generate", response_model=Union[TextGenerationResponse, None])
async def generate_text_v2(
    request_data: Union[OptimizedTextGenerationRequest, TextGenerationRequest, Dict[str, Any]],
    request: Request,
    text_service: TextGenerationService = Depends(get_text_service),
    user_id: str = Depends(get_user_id)
):
    """生成文本（优化版本 API v2）

    这是新的优化版本API端点，提供以下增强功能：
    - 支持多种请求格式（优化版本、传统格式、字典格式）
    - 自动供应商兼容性检查和参数验证
    - 增强的错误处理和日志记录
    - 更好的参数组织和默认值设置

    Args:
        request_data: 文本生成请求（支持多种格式）
        request: FastAPI请求对象
        text_service: 文本生成服务
        user_id: 用户ID

    Returns:
        文本生成响应或流式响应
    """
    try:
        # 适配请求格式
        if isinstance(request_data, dict):
            # 字典格式，转换为优化版本
            optimized_request = OptimizedTextGenerationRequest.from_legacy_request(request_data)
        elif isinstance(request_data, TextGenerationRequest):
            # 传统格式，转换为优化版本
            optimized_request = OptimizedTextGenerationRequest.from_legacy_request(request_data.dict())
        elif isinstance(request_data, OptimizedTextGenerationRequest):
            # 已经是优化版本
            optimized_request = request_data
        else:
            raise HTTPException(status_code=400, detail=f"不支持的请求格式: {type(request_data)}")

        # 使用优化版本的文本生成服务
        if optimized_request.stream.enabled:
            # 流式响应
            response_iter = await text_service.generate_text_optimized(
                optimized_request,
                user_id=user_id,
                request_id=getattr(request.state, "request_id", None)
            )

            async def generate_stream():
                async for chunk in response_iter:
                    yield {
                        "event": "data",
                        "data": chunk.json()
                    }

                # 发送结束标记
                yield {
                    "event": "data",
                    "data": "[DONE]"
                }

            return EventSourceResponse(generate_stream())
        else:
            # 同步响应
            response = await text_service.generate_text_optimized(
                optimized_request,
                user_id=user_id,
                request_id=getattr(request.state, "request_id", None)
            )
            return response

    except AIGenHubException as e:
        raise HTTPException(status_code=400, detail=e.to_dict())
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/v2/validate", response_model=Dict[str, Any])
async def validate_request_compatibility(
    request_data: Union[OptimizedTextGenerationRequest, TextGenerationRequest, Dict[str, Any]],
    provider_name: str,
    request: Request
):
    """验证请求与指定供应商的兼容性（API v2）

    这个端点允许客户端在发送实际请求之前检查兼容性，
    有助于提前发现问题和获取优化建议。

    Args:
        request_data: 文本生成请求
        provider_name: 供应商名称
        request: FastAPI请求对象

    Returns:
        兼容性验证结果
    """
    try:
        # 适配请求格式
        optimized_request = RequestAdapter.adapt_request(request_data)

        # 执行兼容性验证
        validation_result = optimized_request.validate_for_provider(provider_name)

        # 获取供应商能力信息
        capabilities = OptimizedTextGenerationRequest.get_provider_capabilities(provider_name)

        return {
            "provider": provider_name,
            "validation": validation_result,
            "capabilities": capabilities,
            "is_compatible": len(validation_result["errors"]) == 0
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/models", response_model=Dict[str, List[str]])
async def get_supported_models(
    text_service: TextGenerationService = Depends(get_text_service)
):
    """获取支持的文本生成模型列表

    返回按供应商分组的模型列表
    """
    try:
        models = await text_service.get_supported_models()
        return models
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_service_stats(
    text_service: TextGenerationService = Depends(get_text_service)
):
    """获取文本生成服务统计信息"""
    try:
        stats = await text_service.get_service_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 兼容OpenAI API格式的端点
@router.post("/chat/completions", response_model=Union[TextGenerationResponse, None])
async def chat_completions(
    request_data: TextGenerationRequest,
    request: Request,
    text_service: TextGenerationService = Depends(get_text_service),
    user_id: str = Depends(get_user_id)
):
    """OpenAI兼容的聊天完成API
    
    提供与OpenAI Chat Completions API兼容的接口
    """
    return await generate_text(request_data, request, text_service, user_id)
